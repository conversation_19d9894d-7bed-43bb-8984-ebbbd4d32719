{"logs": [{"outputFile": "com.example.hamehame.app-mergeDebugResources-30:/values-ca/values-ca.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9a29a62ff3fc1ea4fe1f5798df91a5f9\\transformed\\material-1.12.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,274,355,435,523,626,718,819,947,1031,1092,1157,1254,1334,1399,1494,1558,1630,1692,1768,1831,1888,2009,2067,2128,2185,2265,2402,2489,2564,2657,2737,2821,2960,3038,3117,3269,3358,3434,3491,3547,3613,3691,3772,3843,3931,4009,4086,4160,4239,4349,4439,4531,4623,4724,4798,4880,4981,5031,5114,5180,5272,5359,5421,5485,5548,5621,5744,5857,5961,6069,6130,6190,6276,6362,6439", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,79,87,102,91,100,127,83,60,64,96,79,64,94,63,71,61,75,62,56,120,57,60,56,79,136,86,74,92,79,83,138,77,78,151,88,75,56,55,65,77,80,70,87,77,76,73,78,109,89,91,91,100,73,81,100,49,82,65,91,86,61,63,62,72,122,112,103,107,60,59,85,85,76,78", "endOffsets": "269,350,430,518,621,713,814,942,1026,1087,1152,1249,1329,1394,1489,1553,1625,1687,1763,1826,1883,2004,2062,2123,2180,2260,2397,2484,2559,2652,2732,2816,2955,3033,3112,3264,3353,3429,3486,3542,3608,3686,3767,3838,3926,4004,4081,4155,4234,4344,4434,4526,4618,4719,4793,4875,4976,5026,5109,5175,5267,5354,5416,5480,5543,5616,5739,5852,5956,6064,6125,6185,6271,6357,6434,6513"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3049,3130,3210,3298,3401,4224,4325,4453,4537,4598,4663,4760,4840,4905,5000,5064,5136,5198,5274,5337,5394,5515,5573,5634,5691,5771,5908,5995,6070,6163,6243,6327,6466,6544,6623,6775,6864,6940,6997,7053,7119,7197,7278,7349,7437,7515,7592,7666,7745,7855,7945,8037,8129,8230,8304,8386,8487,8537,8620,8686,8778,8865,8927,8991,9054,9127,9250,9363,9467,9575,9636,9696,9864,9950,10027", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "endColumns": "12,80,79,87,102,91,100,127,83,60,64,96,79,64,94,63,71,61,75,62,56,120,57,60,56,79,136,86,74,92,79,83,138,77,78,151,88,75,56,55,65,77,80,70,87,77,76,73,78,109,89,91,91,100,73,81,100,49,82,65,91,86,61,63,62,72,122,112,103,107,60,59,85,85,76,78", "endOffsets": "319,3125,3205,3293,3396,3488,4320,4448,4532,4593,4658,4755,4835,4900,4995,5059,5131,5193,5269,5332,5389,5510,5568,5629,5686,5766,5903,5990,6065,6158,6238,6322,6461,6539,6618,6770,6859,6935,6992,7048,7114,7192,7273,7344,7432,7510,7587,7661,7740,7850,7940,8032,8124,8225,8299,8381,8482,8532,8615,8681,8773,8860,8922,8986,9049,9122,9245,9358,9462,9570,9631,9691,9777,9945,10022,10101"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\76a693482785115f724fb0ceaef9fe4b\\transformed\\appcompat-1.7.1\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,228,333,440,523,629,755,839,918,1009,1102,1195,1290,1388,1481,1574,1668,1759,1850,1931,2042,2150,2248,2358,2463,2571,2731,2830", "endColumns": "122,104,106,82,105,125,83,78,90,92,92,94,97,92,92,93,90,90,80,110,107,97,109,104,107,159,98,81", "endOffsets": "223,328,435,518,624,750,834,913,1004,1097,1190,1285,1383,1476,1569,1663,1754,1845,1926,2037,2145,2243,2353,2458,2566,2726,2825,2907"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "324,447,552,659,742,848,974,1058,1137,1228,1321,1414,1509,1607,1700,1793,1887,1978,2069,2150,2261,2369,2467,2577,2682,2790,2950,9782", "endColumns": "122,104,106,82,105,125,83,78,90,92,92,94,97,92,92,93,90,90,80,110,107,97,109,104,107,159,98,81", "endOffsets": "442,547,654,737,843,969,1053,1132,1223,1316,1409,1504,1602,1695,1788,1882,1973,2064,2145,2256,2364,2462,2572,2677,2785,2945,3044,9859"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\55b720bce712d5f10fb40e251130c91a\\transformed\\core-1.13.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,449,555,660,786", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "146,248,347,444,550,655,781,882"}, "to": {"startLines": "38,39,40,41,42,43,44,116", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3493,3589,3691,3790,3887,3993,4098,10106", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "3584,3686,3785,3882,3988,4093,4219,10202"}}]}]}