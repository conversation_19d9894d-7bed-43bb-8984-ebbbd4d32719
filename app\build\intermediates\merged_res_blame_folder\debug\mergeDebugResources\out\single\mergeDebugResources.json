[{"merged": "com.example.hamehame.app-debug-32:/drawable_ic_launcher_foreground.xml.flat", "source": "com.example.hamehame.app-main-34:/drawable/ic_launcher_foreground.xml"}, {"merged": "com.example.hamehame.app-debug-32:/mipmap-mdpi_ic_launcher_round.webp.flat", "source": "com.example.hamehame.app-main-34:/mipmap-mdpi/ic_launcher_round.webp"}, {"merged": "com.example.hamehame.app-debug-32:/mipmap-hdpi_ic_launcher.webp.flat", "source": "com.example.hamehame.app-main-34:/mipmap-hdpi/ic_launcher.webp"}, {"merged": "com.example.hamehame.app-debug-32:/mipmap-xxhdpi_ic_launcher.webp.flat", "source": "com.example.hamehame.app-main-34:/mipmap-xxhdpi/ic_launcher.webp"}, {"merged": "com.example.hamehame.app-debug-32:/drawable_ic_launcher_background.xml.flat", "source": "com.example.hamehame.app-main-34:/drawable/ic_launcher_background.xml"}, {"merged": "com.example.hamehame.app-debug-32:/mipmap-mdpi_ic_launcher.webp.flat", "source": "com.example.hamehame.app-main-34:/mipmap-mdpi/ic_launcher.webp"}, {"merged": "com.example.hamehame.app-debug-32:/xml_backup_rules.xml.flat", "source": "com.example.hamehame.app-main-34:/xml/backup_rules.xml"}, {"merged": "com.example.hamehame.app-debug-32:/mipmap-anydpi_ic_launcher.xml.flat", "source": "com.example.hamehame.app-main-34:/mipmap-anydpi/ic_launcher.xml"}, {"merged": "com.example.hamehame.app-debug-32:/mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "com.example.hamehame.app-main-34:/mipmap-xxxhdpi/ic_launcher_round.webp"}, {"merged": "com.example.hamehame.app-debug-32:/mipmap-xhdpi_ic_launcher.webp.flat", "source": "com.example.hamehame.app-main-34:/mipmap-xhdpi/ic_launcher.webp"}, {"merged": "com.example.hamehame.app-debug-32:/xml_data_extraction_rules.xml.flat", "source": "com.example.hamehame.app-main-34:/xml/data_extraction_rules.xml"}, {"merged": "com.example.hamehame.app-debug-32:/mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "com.example.hamehame.app-main-34:/mipmap-xxhdpi/ic_launcher_round.webp"}, {"merged": "com.example.hamehame.app-debug-32:/mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "com.example.hamehame.app-main-34:/mipmap-xhdpi/ic_launcher_round.webp"}, {"merged": "com.example.hamehame.app-debug-32:/mipmap-hdpi_ic_launcher_round.webp.flat", "source": "com.example.hamehame.app-main-34:/mipmap-hdpi/ic_launcher_round.webp"}, {"merged": "com.example.hamehame.app-debug-32:/layout_activity_main.xml.flat", "source": "com.example.hamehame.app-main-34:/layout/activity_main.xml"}, {"merged": "com.example.hamehame.app-debug-32:/mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "com.example.hamehame.app-main-34:/mipmap-xxxhdpi/ic_launcher.webp"}, {"merged": "com.example.hamehame.app-debug-32:/mipmap-anydpi_ic_launcher_round.xml.flat", "source": "com.example.hamehame.app-main-34:/mipmap-anydpi/ic_launcher_round.xml"}]