package com.todolist.app.data.entities

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "categories")
data class Category(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val name: String,
    val color: String, // Hex color code
    val icon: String, // Icon resource name or unicode
    val userId: Long,
    val createdAt: Long = System.currentTimeMillis()
)
