Tạo 1 dự án todo-list(cấu trúc thư mục dễ hiểu và chuyên nghiệp) :
gồm 1 giao diện chính có : 
 	+ trên cùng bên bên trái là nút = click vào hiển thị menu các chức năng, bên phải là công cụ tìm kiếm task
	+ bên d<PERSON><PERSON><PERSON> là list theo chiều ngang các danh mục ( mắc định có tất cả để lọc tất cả task và màu săc hình biêu tượng hình elip) và 1 nút + thêm danh mục
	+ bên dưới là list các nhiệm vụ, khi click vào sẽ mở 1 cửa sổ hiển thị chi tiết(mỗi task có các task con ví dụ nấu cơm có vo gạo và cắm điện) và 1 nút + ở dưới để thêm task mới;
	+ bên dư<PERSON><PERSON> là 3 nút chuyển flagment nhiệm vụ, lị<PERSON> , c<PERSON> nhân.

sử dụng room database. model task có title, description, ngày bắt đầu (tính theo phút), ngày kết thúc, categoryId, userId, listtask con (có trạng thái hoàn thành), trạng thái hoàn thành
